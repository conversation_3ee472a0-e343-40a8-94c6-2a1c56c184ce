# GitHub Integration Setup Guide

This guide explains how to set up GitHub integration for automatic deployment when products are updated through the admin panel.

## Overview

The GitHub integration allows the admin panel to:
1. Automatically commit product changes to your GitHub repository
2. Trigger Cloudflare Pages deployments via GitHub webhooks
3. Provide real-time status feedback in the admin panel

## Required Environment Variables

Add these environment variables to your Cloudflare Pages project:

### 1. GitHub Repository Information
```bash
GITHUB_OWNER=your-github-username
GITHUB_REPO=your-repository-name
GITHUB_BRANCH=main  # Optional, defaults to 'main'
```

### 2. GitHub Personal Access Token
```bash
GITHUB_TOKEN=ghp_your_personal_access_token_here
```

### 3. Cloudflare Build Hook (Optional Fallback)
```bash
CLOUDFLARE_BUILD_HOOK_URL=https://api.cloudflare.com/client/v4/pages/webhooks/deploy_hooks/your-hook-id
```

## Step-by-Step Setup

### Step 1: Create a GitHub Personal Access Token

1. Go to GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Give it a descriptive name like "Cheers Marketplace Admin"
4. Set expiration (recommend 1 year)
5. Select these scopes:
   - `repo` (Full control of private repositories)
   - `workflow` (Update GitHub Action workflows) - if using GitHub Actions

### Step 2: Add Environment Variables to Cloudflare Pages

1. Go to your Cloudflare Pages dashboard
2. Select your project
3. Go to Settings → Environment variables
4. Add the following variables for **Production**:

```
GITHUB_OWNER = your-github-username
GITHUB_REPO = cheersmarketplace2
GITHUB_TOKEN = ghp_your_token_here
GITHUB_BRANCH = main
```

### Step 3: Configure Cloudflare Pages GitHub Integration

1. In Cloudflare Pages, ensure your project is connected to GitHub
2. Set up automatic deployments on push to main branch
3. The GitHub integration will automatically trigger deployments when the admin panel commits changes

### Step 4: Test the Integration

1. Go to your admin panel: `https://your-site.pages.dev/admin?admin=1`
2. Check the GitHub status indicator in the top-right
3. Try adding/editing a product and clicking "Sync & Deploy"
4. Monitor the deployment in Cloudflare Pages dashboard

## Status Indicators

The admin panel shows GitHub integration status:

- 🟢 **Green**: GitHub connected and working
- 🟡 **Yellow**: GitHub configured but connection issues
- 🔴 **Red**: GitHub not configured
- ❌ **Error**: Failed to check GitHub status

## Troubleshooting

### Common Issues

1. **Token Permissions**: Ensure your GitHub token has `repo` scope
2. **Repository Access**: Token must have access to the repository
3. **Branch Name**: Verify the branch name matches your default branch
4. **Environment Variables**: Check all variables are set in Cloudflare Pages

### Testing GitHub Connection

Visit: `https://your-site.pages.dev/api/github-config`

This will show:
- Whether GitHub is configured
- Connection status
- Repository information
- Any error messages

### Manual Testing

You can test the GitHub integration by making a POST request:

```bash
curl -X POST https://your-site.pages.dev/api/github-config \
  -H "Content-Type: application/json" \
  -d '{"action": "test-commit"}'
```

## Workflow

When you click "Sync & Deploy" in the admin panel:

1. **Local Save**: Products saved to local `products.json`
2. **GitHub Commit**: Changes committed to GitHub repository
3. **Auto Deploy**: Cloudflare Pages detects GitHub changes and rebuilds
4. **Live Update**: Your website updates with new products

## Security Notes

- Keep your GitHub token secure
- Use environment variables, never commit tokens to code
- Consider token expiration and renewal
- Monitor GitHub token usage in your GitHub settings

## Alternative: Manual Deployment

If GitHub integration isn't set up, you can still:
1. Export products from admin panel
2. Manually update `src/data/products.json` in your repository
3. Commit and push changes
4. Cloudflare Pages will deploy automatically

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify environment variables in Cloudflare Pages
3. Test the GitHub API endpoints
4. Check Cloudflare Pages deployment logs
