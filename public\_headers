# This file configures caching rules for Cloudflare Pages.

# Cache framework assets (like CSS and JS with hashes in the name) for one year.
# The 'immutable' directive tells the browser it never needs to check for an update.
/_astro/*
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff

# Cache other static assets like images, fonts, and icons for one day.
/assets/*
  Cache-Control: public, max-age=86400
  X-Content-Type-Options: nosniff
/favicon.svg
  Cache-Control: public, max-age=86400
  X-Content-Type-Options: nosniff

# Cache API responses for 5 minutes
/api/products.json
  Cache-Control: public, max-age=300, s-maxage=300

# Cache HTML pages for 1 hour with revalidation
/*
  Cache-Control: public, max-age=3600, must-revalidate
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Security headers for admin
/admin
  X-Robots-Tag: noindex, nofollow
  Cache-Control: no-cache, no-store, must-revalidate
