---
// Mobile-optimized header with hamburger menu
---
<header class="site-header">
  <div class="container header-flex">
    <a href="/" class="logo">
      <img src="/favicon.svg" alt="Cheers Marketplace Logo" width="40" height="40" loading="eager" fetchpriority="high" />
      <span class="logo-text">Cheers Marketplace</span>
    </a>

    <!-- Mobile hamburger button -->
    <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
    </button>

    <!-- Navigation menu -->
    <nav class="main-nav" id="main-nav">
      <a href="/">Home</a>
      <a href="/products">Products</a>
      <a href="/about">About</a>
      <a href="/faq">FAQ</a>
      <a href="/cart">Cart</a>
    </nav>
  </div>
</header>

<!-- Optimized mobile menu JavaScript -->
<script is:inline>
  document.addEventListener('DOMContentLoaded', () => {
    const toggle = document.querySelector('.mobile-menu-toggle');
    const nav = document.querySelector('.main-nav');

    if (toggle && nav) {
      toggle.addEventListener('click', () => {
        const isOpen = nav.classList.contains('nav-open');
        nav.classList.toggle('nav-open');
        toggle.setAttribute('aria-expanded', !isOpen);
        toggle.classList.toggle('menu-open');
      });

      // Close menu when clicking nav links
      nav.addEventListener('click', (e) => {
        if (e.target.tagName === 'A') {
          nav.classList.remove('nav-open');
          toggle.setAttribute('aria-expanded', 'false');
          toggle.classList.remove('menu-open');
        }
      });
    }
  });
</script>
