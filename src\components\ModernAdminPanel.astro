---
// Modern Admin Panel optimized for Astro/Cloudflare Pages
import { getAllProducts } from '../lib/products.ts';

// Load products at build time for initial display
const initialProducts = await getAllProducts();
---

<!-- Admin Access Check -->
<div id="not-admin" class="access-denied" style="display: none;">
  <div class="access-denied-content">
    <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
    </svg>
    <h2>Access Denied</h2>
    <p>This area is restricted to administrators only.</p>
    <a href="/" class="btn-back-home">← Back to Home</a>
  </div>
</div>

<div id="admin-content" class="admin-container" style="display: none;">
  <!-- Admin Header -->
  <div class="admin-header">
    <div class="admin-header-content">
      <div class="admin-title">
        <h1>Product Management</h1>
        <p>Manage your product catalog</p>
      </div>
      <div class="admin-actions">
        <div class="github-status" id="github-status">
          <span class="status-indicator" id="github-indicator">⚪</span>
          <span class="status-text" id="github-text">Checking GitHub...</span>
        </div>
        <button id="sync-products" class="btn-sync" title="Sync with server and deploy">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,18A6,6 0 0,1 6,12C6,11 6.25,10.03 6.7,9.2L5.24,7.74C4.46,8.97 4,10.43 4,12A8,8 0 0,0 12,20V23L16,19L12,15M12,4V1L8,5L12,9V6A6,6 0 0,1 18,12C18,13 17.75,13.97 17.3,14.8L18.76,16.26C19.54,15.03 20,13.57 20,12A8,8 0 0,0 12,4Z"/>
          </svg>
          Sync & Deploy
        </button>
        <button id="add-product-btn" class="btn-primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
          </svg>
          Add Product
        </button>
      </div>
    </div>
  </div>

  <!-- Admin Navigation -->
  <div class="admin-nav">
    <button id="tab-list" class="admin-tab-btn active" data-tab="list">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8V5H4M4,19H8V15H4M4,14H8V10H4V14Z"/>
      </svg>
      Product List
    </button>
    <button id="tab-form" class="admin-tab-btn" data-tab="form">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
      </svg>
      Add/Edit Product
    </button>
    <button id="tab-categories" class="admin-tab-btn" data-tab="categories">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z"/>
      </svg>
      Categories
    </button>
  </div>

  <!-- Product List Section -->
  <div id="section-list" class="admin-section active">
    <!-- Filters and Search -->
    <div class="admin-controls">
      <div class="admin-stats">
        <div class="stat-card">
          <span class="stat-number" id="total-products">0</span>
          <span class="stat-label">Total Products</span>
        </div>
        <div class="stat-card">
          <span class="stat-number" id="visible-products">0</span>
          <span class="stat-label">Visible</span>
        </div>
        <div class="stat-card">
          <span class="stat-number" id="categories-count">0</span>
          <span class="stat-label">Categories</span>
        </div>
      </div>
      
      <div class="admin-filters">
        <div class="filter-group">
          <label for="admin-category-filter">Category</label>
          <select id="admin-category-filter" class="admin-select">
            <option value="">All Categories</option>
            <option value="Clothing">Clothing</option>
            <option value="Books">Books</option>
            <option value="Arts & Crafts">Arts & Crafts</option>
            <option value="Home Decor">Home Decor</option>
            <option value="Electronics">Electronics</option>
            <option value="Toys & Games">Toys & Games</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label for="admin-sort-by">Sort By</label>
          <select id="admin-sort-by" class="admin-select">
            <option value="name">Name</option>
            <option value="price">Price</option>
            <option value="category">Category</option>
            <option value="newest">Newest</option>
          </select>
        </div>
        
        <div class="filter-group search-group">
          <label for="admin-search">Search</label>
          <div class="search-input-wrapper">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
            </svg>
            <input 
              id="admin-search" 
              type="search" 
              placeholder="Search products..." 
              class="admin-input"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Loading and Empty States -->
    <div id="loading-state" class="state-container" style="display: none;">
      <div class="loading-spinner"></div>
      <p>Loading products...</p>
    </div>
    
    <div id="empty-state" class="state-container" style="display: none;">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
      </svg>
      <h3>No products found</h3>
      <p>Start by adding your first product to the catalog.</p>
      <button class="btn-primary" onclick="document.getElementById('tab-form').click()">
        Add Your First Product
      </button>
    </div>

    <!-- Products Table -->
    <div class="admin-table-wrapper">
      <table id="products-table" class="admin-products-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Category</th>
            <th>Price</th>
            <th>Images</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody id="products-tbody">
          <!-- Products will be loaded here -->
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div id="pagination" class="admin-pagination">
      <!-- Pagination will be loaded here -->
    </div>
  </div>

  <!-- Product Form Section -->
  <div id="section-form" class="admin-section">
    <div class="form-header">
      <h2 id="form-title">Add New Product</h2>
      <div class="form-actions">
        <button id="cancel-form" class="btn-secondary">Cancel</button>
        <button id="save-product" class="btn-primary">Save Product</button>
      </div>
    </div>

    <form id="product-form" class="product-form">
      <!-- Form content will be added via JavaScript for better interactivity -->
    </form>
  </div>

  <!-- Categories Management Section -->
  <div id="section-categories" class="admin-section">
    <div class="form-header">
      <h2>Manage Categories</h2>
      <div class="form-actions">
        <button id="add-category-btn" class="btn-primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
          </svg>
          Add Category
        </button>
      </div>
    </div>

    <div class="categories-container">
      <div class="categories-list">
        <h3>Current Categories</h3>
        <div id="categories-grid" class="categories-grid">
          <!-- Categories will be loaded here -->
        </div>
      </div>

      <div class="category-form-container" id="category-form-container" style="display: none;">
        <h3 id="category-form-title">Add New Category</h3>
        <form id="category-form" class="category-form">
          <div class="form-group">
            <label for="category-name">Category Name *</label>
            <input type="text" id="category-name" name="name" required class="form-input" />
          </div>
          <div class="form-group">
            <label for="category-description">Description</label>
            <textarea id="category-description" name="description" class="form-input" rows="3"></textarea>
          </div>
          <div class="form-actions">
            <button type="button" id="cancel-category" class="btn-secondary">Cancel</button>
            <button type="button" id="save-category" class="btn-primary">Save Category</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script define:vars={{ initialProducts }} is:inline>
  // Modern Admin Panel with proper data integration
  class ModernAdminPanel {
    constructor() {
      this.products = [...initialProducts];
      this.filteredProducts = [...this.products];
      this.categories = this.getCategories();
      this.currentPage = 1;
      this.itemsPerPage = 20;
      this.currentFilter = '';
      this.currentSearch = '';
      this.currentSort = 'name';
      this.editingProduct = null;
      this.editingCategory = null;
      this.keyPoints = [];

      // Track unsaved changes
      this.hasUnsavedChanges = false;
      this.lastSyncedProducts = [...initialProducts];

      this.init();
    }
    
    init() {
      this.checkAdminAccess();
      this.bindEvents();
      this.renderStats();
      this.renderProducts();
      this.initializeForm();
      this.checkGitHubStatus();
      this.updateSyncButtonState();
    }
    
    checkAdminAccess() {
      const isAdmin = new URLSearchParams(window.location.search).get('admin') === '1';
      const adminContent = document.getElementById('admin-content');
      const notAdmin = document.getElementById('not-admin');
      
      if (isAdmin) {
        adminContent.style.display = 'block';
        notAdmin.style.display = 'none';
      } else {
        adminContent.style.display = 'none';
        notAdmin.style.display = 'block';
      }
    }
    
    bindEvents() {
      // Tab navigation
      document.querySelectorAll('.admin-tab-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const target = e.target.closest('.admin-tab-btn');
          if (target && target.dataset.tab) {
            this.switchTab(target.dataset.tab);
          }
        });
      });

      // Filters and search
      const categoryFilter = document.getElementById('admin-category-filter');
      const sortBy = document.getElementById('admin-sort-by');
      const searchInput = document.getElementById('admin-search');

      if (categoryFilter) {
        categoryFilter.addEventListener('change', (e) => {
          this.currentFilter = e.target.value;
          this.filterProducts();
        });
      }

      if (sortBy) {
        sortBy.addEventListener('change', (e) => {
          this.currentSort = e.target.value;
          this.filterProducts();
        });
      }

      if (searchInput) {
        searchInput.addEventListener('input', (e) => {
          this.currentSearch = e.target.value;
          this.debounce(() => this.filterProducts(), 300)();
        });
      }

      // Action buttons
      const addProductBtn = document.getElementById('add-product-btn');
      const syncBtn = document.getElementById('sync-products');
      const cancelBtn = document.getElementById('cancel-form');
      const saveBtn = document.getElementById('save-product');

      if (addProductBtn) {
        addProductBtn.addEventListener('click', () => {
          this.editingProduct = null;
          this.switchTab('form');
          this.resetForm();
        });
      }

      if (syncBtn) {
        syncBtn.addEventListener('click', () => {
          this.syncProducts();
        });
      }

      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          this.switchTab('list');
        });
      }

      if (saveBtn) {
        saveBtn.addEventListener('click', (e) => {
          e.preventDefault();
          this.saveProduct();
        });
      }

      // Category management events
      const addCategoryBtn = document.getElementById('add-category-btn');
      const saveCategoryBtn = document.getElementById('save-category');
      const cancelCategoryBtn = document.getElementById('cancel-category');

      if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', () => {
          this.showCategoryForm();
        });
      }

      if (saveCategoryBtn) {
        saveCategoryBtn.addEventListener('click', (e) => {
          e.preventDefault();
          this.saveCategory();
        });
      }

      if (cancelCategoryBtn) {
        cancelCategoryBtn.addEventListener('click', () => {
          this.hideCategoryForm();
        });
      }
    }
    
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }
    
    switchTab(tab) {
      // Update tab buttons
      document.querySelectorAll('.admin-tab-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tab);
      });

      // Update sections
      document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.toggle('active', section.id === `section-${tab}`);
      });

      // Load categories if switching to categories tab
      if (tab === 'categories') {
        this.renderCategories();
      }
    }
    
    renderStats() {
      const totalProducts = this.products.length;
      const visibleProducts = this.filteredProducts.length;
      const categories = new Set(this.products.map(p => p.category)).size;

      const totalEl = document.getElementById('total-products');
      const visibleEl = document.getElementById('visible-products');
      const categoriesEl = document.getElementById('categories-count');

      if (totalEl) totalEl.textContent = totalProducts;
      if (visibleEl) visibleEl.textContent = visibleProducts;
      if (categoriesEl) categoriesEl.textContent = categories;
    }

    filterProducts() {
      let filtered = [...this.products];

      // Apply category filter
      if (this.currentFilter) {
        filtered = filtered.filter(p => p.category === this.currentFilter);
      }

      // Apply search filter
      if (this.currentSearch) {
        const search = this.currentSearch.toLowerCase();
        filtered = filtered.filter(p =>
          p.name.toLowerCase().includes(search) ||
          p.description.toLowerCase().includes(search) ||
          p.category.toLowerCase().includes(search)
        );
      }

      // Apply sorting
      filtered.sort((a, b) => {
        switch (this.currentSort) {
          case 'price':
            return a.price - b.price;
          case 'category':
            return a.category.localeCompare(b.category);
          case 'newest':
            return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
          default:
            return a.name.localeCompare(b.name);
        }
      });

      this.filteredProducts = filtered;
      this.currentPage = 1;
      this.renderProducts();
      this.renderStats();
    }
    
    filterProducts() {
      let filtered = [...this.products];
      
      // Apply category filter
      if (this.currentFilter) {
        filtered = filtered.filter(p => p.category === this.currentFilter);
      }
      
      // Apply search filter
      if (this.currentSearch) {
        const search = this.currentSearch.toLowerCase();
        filtered = filtered.filter(p => 
          p.name.toLowerCase().includes(search) ||
          p.description.toLowerCase().includes(search) ||
          p.category.toLowerCase().includes(search)
        );
      }
      
      // Apply sorting
      filtered.sort((a, b) => {
        switch (this.currentSort) {
          case 'price':
            return a.price - b.price;
          case 'category':
            return a.category.localeCompare(b.category);
          case 'newest':
            return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);
          default:
            return a.name.localeCompare(b.name);
        }
      });
      
      this.filteredProducts = filtered;
      this.currentPage = 1;
      this.renderProducts();
      this.renderStats();
    }
    
    renderProducts() {
      const tbody = document.getElementById('products-tbody');
      const emptyState = document.getElementById('empty-state');
      const table = document.getElementById('products-table');

      if (!tbody) return;

      if (this.filteredProducts.length === 0) {
        table.style.display = 'none';
        if (emptyState) emptyState.style.display = 'flex';
        return;
      }

      table.style.display = 'table';
      if (emptyState) emptyState.style.display = 'none';

      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;
      const pageProducts = this.filteredProducts.slice(startIndex, endIndex);

      tbody.innerHTML = pageProducts.map((product, index) => {
        const actualIndex = startIndex + index;
        const imageCount = product.images?.length || 0;
        const hasDefects = product.defects && product.defects.trim();
        const changeStatus = this.isProductChanged(product);

        return `
          <tr class="product-row ${changeStatus ? `product-${changeStatus}` : ''}" data-id="${product.id}" data-index="${actualIndex}">
            <td class="product-name-cell">
              <div class="product-name-container">
                ${changeStatus ? `<span class="change-indicator ${changeStatus}" title="${changeStatus === 'new' ? 'New product' : 'Modified product'}">${changeStatus === 'new' ? '●' : '◐'}</span>` : ''}
                <strong>${this.escapeHtml(product.name)}</strong>
                ${product.description ? `<div class="product-description-preview">${this.escapeHtml(product.description.substring(0, 100))}${product.description.length > 100 ? '...' : ''}</div>` : ''}
              </div>
            </td>
            <td class="product-category-cell">
              <span class="category-badge">${this.escapeHtml(product.category)}</span>
            </td>
            <td class="product-price-cell">
              <strong>$${Number(product.price).toFixed(2)}</strong>
            </td>
            <td class="product-images-cell">
              <span class="images-count">${imageCount} image${imageCount !== 1 ? 's' : ''}</span>
            </td>
            <td class="product-status-cell">
              <div class="status-indicators">
                ${hasDefects ? '<span class="status-badge defects">Has Issues</span>' : '<span class="status-badge good">Good</span>'}
              </div>
            </td>
            <td class="product-actions-cell">
              <div class="action-buttons">
                <button class="btn-edit" data-index="${actualIndex}" title="Edit Product">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
                  </svg>
                </button>
                <button class="btn-delete" data-index="${actualIndex}" title="Delete Product">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        `;
      }).join('');

      // Bind events to the new buttons
      this.bindProductActions();

      this.renderPagination();
    }

    escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    getCategories() {
      const categories = new Set(this.products.map(p => p.category));
      return Array.from(categories).sort();
    }

    renderCategories() {
      const grid = document.getElementById('categories-grid');
      if (!grid) return;

      this.categories = this.getCategories();

      if (this.categories.length === 0) {
        grid.innerHTML = '<p class="no-categories">No categories yet. Add your first category!</p>';
        return;
      }

      grid.innerHTML = this.categories.map(category => {
        const productCount = this.products.filter(p => p.category === category).length;
        return `
          <div class="category-card">
            <div class="category-info">
              <h4>${this.escapeHtml(category)}</h4>
              <p>${productCount} product${productCount !== 1 ? 's' : ''}</p>
            </div>
            <div class="category-actions">
              <button class="btn-edit-category" data-category="${this.escapeHtml(category)}" title="Edit Category">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
                </svg>
              </button>
              <button class="btn-delete-category" data-category="${this.escapeHtml(category)}" title="Delete Category">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                </svg>
              </button>
            </div>
          </div>
        `;
      }).join('');

      this.bindCategoryActions();
    }

    bindCategoryActions() {
      document.querySelectorAll('.btn-edit-category').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const category = e.currentTarget.dataset.category;
          this.editCategory(category);
        });
      });

      document.querySelectorAll('.btn-delete-category').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const category = e.currentTarget.dataset.category;
          this.deleteCategory(category);
        });
      });
    }

    showCategoryForm(category = null) {
      const container = document.getElementById('category-form-container');
      const title = document.getElementById('category-form-title');
      const nameInput = document.getElementById('category-name');
      const descInput = document.getElementById('category-description');

      if (!container || !title || !nameInput) return;

      this.editingCategory = category;

      if (category) {
        title.textContent = 'Edit Category';
        nameInput.value = category;
        if (descInput) descInput.value = '';
      } else {
        title.textContent = 'Add New Category';
        nameInput.value = '';
        if (descInput) descInput.value = '';
      }

      container.style.display = 'block';
      nameInput.focus();
    }

    hideCategoryForm() {
      const container = document.getElementById('category-form-container');
      if (container) {
        container.style.display = 'none';
      }
      this.editingCategory = null;
    }

    editCategory(category) {
      this.showCategoryForm(category);
    }

    async deleteCategory(category) {
      const productCount = this.products.filter(p => p.category === category).length;

      if (productCount > 0) {
        if (!confirm(`This category has ${productCount} product${productCount !== 1 ? 's' : ''}. Deleting it will remove the category from all products. Continue?`)) {
          return;
        }

        // Remove category from all products
        this.products.forEach(product => {
          if (product.category === category) {
            product.category = 'Uncategorized';
          }
        });
      } else {
        if (!confirm(`Are you sure you want to delete the "${category}" category?`)) {
          return;
        }
      }

      try {
        // Update localStorage
        localStorage.setItem('cheers_products', JSON.stringify(this.products));

        // Mark as changed (don't auto-sync)
        this.markAsChanged();

        // Re-render
        this.categories = this.getCategories();
        this.renderCategories();
        this.filterProducts();

        this.showNotification('Category deleted successfully!', 'success');
      } catch (error) {
        console.error('Delete category error:', error);
        this.showNotification('Failed to delete category', 'error');
      }
    }

    async saveCategory() {
      const nameInput = document.getElementById('category-name');
      const descInput = document.getElementById('category-description');

      if (!nameInput) {
        this.showNotification('Category name field not found', 'error');
        return;
      }

      const newName = nameInput.value.trim();

      if (!newName) {
        this.showNotification('Category name is required', 'error');
        return;
      }

      // Check if category already exists (and it's not the one we're editing)
      if (this.categories.includes(newName) && newName !== this.editingCategory) {
        this.showNotification('Category already exists', 'error');
        return;
      }

      try {
        if (this.editingCategory) {
          // Update existing category
          this.products.forEach(product => {
            if (product.category === this.editingCategory) {
              product.category = newName;
            }
          });
        } else {
          // New category - no need to do anything special
          // It will be available when products are assigned to it
        }

        // Update localStorage
        localStorage.setItem('cheers_products', JSON.stringify(this.products));

        // Mark as changed (don't auto-sync)
        this.markAsChanged();

        // Re-render
        this.categories = this.getCategories();
        this.renderCategories();
        this.filterProducts();
        this.hideCategoryForm();

        this.showNotification(
          this.editingCategory ? 'Category updated successfully!' : 'Category ready for use!',
          'success'
        );
      } catch (error) {
        console.error('Save category error:', error);
        this.showNotification('Failed to save category', 'error');
      }
    }

    bindProductActions() {
      // Bind edit buttons
      document.querySelectorAll('.btn-edit').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const index = parseInt(e.currentTarget.dataset.index);
          this.editProduct(index);
        });
      });

      // Bind delete buttons
      document.querySelectorAll('.btn-delete').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const index = parseInt(e.currentTarget.dataset.index);
          this.deleteProduct(index);
        });
      });
    }
    
    renderPagination() {
      const pagination = document.getElementById('pagination');
      const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
      
      if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
      }
      
      let paginationHTML = '';
      
      // Previous button
      if (this.currentPage > 1) {
        paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${this.currentPage - 1})">Previous</button>`;
      }
      
      // Page numbers
      for (let i = 1; i <= totalPages; i++) {
        if (i === this.currentPage) {
          paginationHTML += `<button class="page-btn active">${i}</button>`;
        } else {
          paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${i})">${i}</button>`;
        }
      }
      
      // Next button
      if (this.currentPage < totalPages) {
        paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${this.currentPage + 1})">Next</button>`;
      }
      
      pagination.innerHTML = paginationHTML;
    }
    
    goToPage(page) {
      this.currentPage = page;
      this.renderProducts();
    }
    
    async checkGitHubStatus() {
      const indicator = document.getElementById('github-indicator');
      const text = document.getElementById('github-text');

      if (!indicator || !text) return;

      try {
        const response = await fetch('/api/github-config');
        const status = await response.json();

        if (status.configured && status.connected) {
          indicator.textContent = '🟢';
          text.textContent = `GitHub: ${status.repository?.name || 'Connected'}`;
          indicator.title = 'GitHub integration active';
        } else if (status.configured && !status.connected) {
          indicator.textContent = '🟡';
          text.textContent = 'GitHub: Connection issue';
          indicator.title = status.error || 'GitHub connection failed';
        } else {
          indicator.textContent = '🔴';
          text.textContent = 'GitHub: Not configured';
          indicator.title = 'GitHub integration not set up';
        }
      } catch (error) {
        console.error('GitHub status check failed:', error);
        const indicator = document.getElementById('github-indicator');
        const text = document.getElementById('github-text');
        if (indicator && text) {
          indicator.textContent = '❌';
          text.textContent = 'GitHub: Error';
          indicator.title = 'Failed to check GitHub status';
        }
      }
    }

    // Track unsaved changes
    markAsChanged() {
      this.hasUnsavedChanges = true;
      this.updateSyncButtonState();
    }

    markAsSynced() {
      this.hasUnsavedChanges = false;
      this.lastSyncedProducts = JSON.parse(JSON.stringify(this.products));
      this.updateSyncButtonState();
    }

    updateSyncButtonState() {
      const syncBtn = document.getElementById('sync-products');
      if (!syncBtn) return;

      if (this.hasUnsavedChanges) {
        syncBtn.classList.add('has-changes');
        syncBtn.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,18A6,6 0 0,1 6,12C6,11 6.25,10.03 6.7,9.2L5.24,7.74C4.46,8.97 4,10.43 4,12A8,8 0 0,0 12,20V23L16,19L12,15M12,4V1L8,5L12,9V6A6,6 0 0,1 18,12C18,13 17.75,13.97 17.3,14.8L18.76,16.26C19.54,15.03 20,13.57 20,12A8,8 0 0,0 12,4Z"/>
          </svg>
          Sync & Deploy (${this.getChangeCount()} changes)
        `;
        syncBtn.title = 'You have unsaved changes. Click to sync and deploy.';
      } else {
        syncBtn.classList.remove('has-changes');
        syncBtn.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,18A6,6 0 0,1 6,12C6,11 6.25,10.03 6.7,9.2L5.24,7.74C4.46,8.97 4,10.43 4,12A8,8 0 0,0 12,20V23L16,19L12,15M12,4V1L8,5L12,9V6A6,6 0 0,1 18,12C18,13 17.75,13.97 17.3,14.8L18.76,16.26C19.54,15.03 20,13.57 20,12A8,8 0 0,0 12,4Z"/>
          </svg>
          Sync & Deploy
        `;
        syncBtn.title = 'All changes are saved and deployed.';
      }
    }

    getChangeCount() {
      // Compare current products with last synced products
      const currentIds = new Set(this.products.map(p => p.id));
      const syncedIds = new Set(this.lastSyncedProducts.map(p => p.id));

      let changes = 0;

      // Count deleted products
      changes += this.lastSyncedProducts.filter(p => !currentIds.has(p.id)).length;

      // Count new products
      changes += this.products.filter(p => !syncedIds.has(p.id)).length;

      // Count modified products
      changes += this.products.filter(current => {
        const synced = this.lastSyncedProducts.find(p => p.id === current.id);
        return synced && JSON.stringify(current) !== JSON.stringify(synced);
      }).length;

      return changes;
    }

    isProductChanged(product) {
      const syncedIds = new Set(this.lastSyncedProducts.map(p => p.id));

      // New product
      if (!syncedIds.has(product.id)) {
        return 'new';
      }

      // Modified product
      const synced = this.lastSyncedProducts.find(p => p.id === product.id);
      if (synced && JSON.stringify(product) !== JSON.stringify(synced)) {
        return 'modified';
      }

      return false;
    }

    async syncProducts() {
      const syncBtn = document.getElementById('sync-products');
      if (!syncBtn) return;

      const originalText = syncBtn.innerHTML;

      syncBtn.innerHTML = '<div class="spinner-small"></div> Syncing...';
      syncBtn.disabled = true;

      try {
        const response = await fetch('/api/products.json');
        if (response.ok) {
          const serverProducts = await response.json();
          this.products = serverProducts;
          this.filteredProducts = [...this.products];
          this.filterProducts();

          // Show success message
          this.showNotification('Products synced successfully!', 'success');
        } else {
          throw new Error('Failed to sync products');
        }
      } catch (error) {
        console.error('Sync error:', error);
        this.showNotification('Failed to sync products', 'error');
      } finally {
        syncBtn.innerHTML = originalText;
        syncBtn.disabled = false;
      }
    }
    
    editProduct(index) {
      this.editingProduct = this.filteredProducts[index];
      this.switchTab('form');
      this.populateForm(this.editingProduct);
    }
    
    async deleteProduct(index) {
      const product = this.filteredProducts[index];

      if (!product) {
        this.showNotification('Product not found', 'error');
        return;
      }

      if (!confirm(`Are you sure you want to delete "${product.name}"?`)) {
        return;
      }

      try {
        // Remove from local array
        const productIndex = this.products.findIndex(p => p.id === product.id);
        if (productIndex > -1) {
          this.products.splice(productIndex, 1);
        }

        // Update localStorage
        localStorage.setItem('cheers_products', JSON.stringify(this.products));

        // Mark as changed (don't auto-sync)
        this.markAsChanged();

        // Re-render
        this.filteredProducts = [...this.products];
        this.filterProducts();

        this.showNotification('Product deleted successfully!', 'success');
      } catch (error) {
        console.error('Delete error:', error);
        this.showNotification('Failed to delete product', 'error');
      }
    }
    
    initializeForm() {
      const form = document.getElementById('product-form');
      if (!form) return;

      this.renderForm();
    }

    renderForm() {
      const form = document.getElementById('product-form');
      if (!form) return;

      const categoryOptions = this.getCategories().map(cat =>
        `<option value="${this.escapeHtml(cat)}">${this.escapeHtml(cat)}</option>`
      ).join('');

      form.innerHTML = `
        <div class="professional-form-wrapper">
          <!-- Form Header -->
          <div class="form-header-section">
            <div class="form-header-content">
              <div class="form-header-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
              </div>
              <div class="form-header-text">
                <h2 id="form-main-title">Add New Product</h2>
                <p>Create a new product listing with detailed information</p>
              </div>
            </div>
          </div>

          <!-- Main Form Content -->
          <div class="form-content-grid">
            <!-- Primary Information Card -->
            <div class="form-card primary-card">
              <div class="card-header">
                <div class="card-icon primary-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z"/>
                  </svg>
                </div>
                <div class="card-title">
                  <h3>Product Information</h3>
                  <span>Essential product details</span>
                </div>
              </div>
              <div class="card-content">
                <div class="input-group">
                  <label class="input-label">Product Name <span class="required">*</span></label>
                  <input type="text" id="product-name" name="name" required class="form-input" placeholder="Enter a descriptive product name" />
                </div>

                <div class="input-row">
                  <div class="input-group">
                    <label class="input-label">Price <span class="required">*</span></label>
                    <div class="price-input-wrapper">
                      <span class="currency-prefix">$</span>
                      <input type="number" id="product-price" name="price" step="0.01" min="0" required class="form-input price-input" placeholder="0.00" />
                    </div>
                  </div>
                  <div class="input-group">
                    <label class="input-label">Category <span class="required">*</span></label>
                    <select id="product-category" name="category" required class="form-input form-select">
                      <option value="">Choose a category</option>
                      ${categoryOptions}
                      <option value="__new__">+ Create New Category</option>
                    </select>
                  </div>
                </div>

                <div class="input-group" id="new-category-group" style="display: none;">
                  <label class="input-label">New Category Name <span class="required">*</span></label>
                  <input type="text" id="new-category-name" class="form-input" placeholder="Enter category name" />
                </div>

                <div class="input-group">
                  <label class="input-label">Product Description <span class="required">*</span></label>
                  <textarea id="product-description" name="description" required class="form-input form-textarea" rows="4" placeholder="Provide a detailed description of your product, including its features, benefits, and any unique characteristics..."></textarea>
                </div>
              </div>
            </div>

            <!-- Media & Features Section -->
            <div class="form-card secondary-card">
              <div class="card-header">
                <div class="card-icon secondary-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
                  </svg>
                </div>
                <div class="card-title">
                  <h3>Product Media</h3>
                  <span>Images and visual content</span>
                </div>
              </div>
              <div class="card-content">
                <div class="input-group">
                  <label class="input-label">Product Images</label>
                  <div class="image-upload-area">
                    <textarea id="product-images" name="images" class="form-input form-textarea image-textarea" rows="4" placeholder="https://your-cdn.com/image1.jpg
https://your-cdn.com/image2.jpg
https://your-cdn.com/image3.jpg"></textarea>
                    <div class="input-help">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                      </svg>
                      <span>Enter one image URL per line. The first image will be the main product photo.</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Product Features Section -->
            <div class="form-card features-card">
              <div class="card-header">
                <div class="card-icon features-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z"/>
                  </svg>
                </div>
                <div class="card-title">
                  <h3>Key Features</h3>
                  <span>Product specifications and attributes</span>
                </div>
              </div>
              <div class="card-content">
                <div id="keypoints-list" class="features-display-area"></div>

                <div class="features-input-section">
                  <div class="features-input-grid">
                    <div class="input-group">
                      <label class="input-label">Feature Name</label>
                      <input type="text" id="new-key-label" placeholder="e.g., Brand, Material, Size" class="form-input" />
                    </div>
                    <div class="input-group">
                      <label class="input-label">Feature Value</label>
                      <input type="text" id="new-key-value" placeholder="e.g., Artisan Pottery, Cotton, Large" class="form-input" />
                    </div>
                  </div>
                  <button type="button" id="add-keypoint-btn" class="add-feature-button">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                    </svg>
                    Add Feature
                  </button>
                  <div class="input-help">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                    <span>Add product specifications like brand, material, dimensions, capacity, etc.</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Notes Section -->
            <div class="form-card notes-card">
              <div class="card-header">
                <div class="card-icon notes-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                  </svg>
                </div>
                <div class="card-title">
                  <h3>Additional Notes</h3>
                  <span>Defects, wear, or special conditions</span>
                </div>
              </div>
              <div class="card-content">
                <div class="input-group">
                  <label class="input-label">Condition Notes</label>
                  <textarea id="product-defects" name="defects" class="form-input form-textarea" rows="3" placeholder="Describe any defects, wear, or special conditions (optional)"></textarea>
                  <div class="input-help warning-help">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,2L13.09,8.26L22,9L17.5,13.74L18.18,22L12,19.77L5.82,22L6.5,13.74L2,9L10.91,8.26L12,2Z"/>
                    </svg>
                    <span>Being transparent about imperfections builds customer trust and reduces returns.</span>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      `;

      // Bind category change event
      const categorySelect = document.getElementById('product-category');
      const newCategoryGroup = document.getElementById('new-category-group');

      if (categorySelect && newCategoryGroup) {
        categorySelect.addEventListener('change', (e) => {
          if (e.target.value === '__new__') {
            newCategoryGroup.style.display = 'block';
            document.getElementById('new-category-name')?.focus();
          } else {
            newCategoryGroup.style.display = 'none';
          }
        });
      }

      // Bind key features functionality
      this.bindKeyFeaturesEvents();
      this.renderKeyFeatures();
    }

    bindKeyFeaturesEvents() {
      const addBtn = document.getElementById('add-keypoint-btn');
      if (addBtn) {
        addBtn.addEventListener('click', () => {
          const labelInput = document.getElementById('new-key-label');
          const valueInput = document.getElementById('new-key-value');

          if (labelInput && valueInput) {
            const label = labelInput.value.trim();
            const value = valueInput.value.trim();

            if (label && value) {
              this.keyPoints.push({ label, value });
              labelInput.value = '';
              valueInput.value = '';
              this.renderKeyFeatures();
            }
          }
        });
      }
    }

    renderKeyFeatures() {
      const container = document.getElementById('keypoints-list');
      if (!container) return;

      container.innerHTML = this.keyPoints.map((kp, index) => `
        <div class="keypoint-item">
          <span class="keypoint-label">${this.escapeHtml(kp.label)}:</span>
          <span class="keypoint-value">${this.escapeHtml(kp.value)}</span>
          <button type="button" class="btn-remove-keypoint" onclick="adminPanel.removeKeyFeature(${index})">×</button>
        </div>
      `).join('');
    }

    removeKeyFeature(index) {
      this.keyPoints.splice(index, 1);
      this.renderKeyFeatures();
    }

    populateForm(product) {
      const formMainTitle = document.getElementById('form-main-title');
      const nameInput = document.getElementById('product-name');
      const categoryInput = document.getElementById('product-category');
      const priceInput = document.getElementById('product-price');
      const descriptionInput = document.getElementById('product-description');
      const imagesInput = document.getElementById('product-images');
      const defectsInput = document.getElementById('product-defects');

      if (formMainTitle) formMainTitle.textContent = 'Edit Product';
      if (nameInput) nameInput.value = product.name || '';
      if (categoryInput) categoryInput.value = product.category || '';
      if (priceInput) priceInput.value = product.price || '';
      if (descriptionInput) descriptionInput.value = product.description || '';
      if (imagesInput) imagesInput.value = (product.images || []).join('\n');
      if (defectsInput) defectsInput.value = product.defects || '';

      // Populate key features
      this.keyPoints = Array.isArray(product.keyPoints) ? [...product.keyPoints] : [];
      this.renderKeyFeatures();

      // Hide new category group if visible
      const newCategoryGroup = document.getElementById('new-category-group');
      if (newCategoryGroup) {
        newCategoryGroup.style.display = 'none';
      }
    }
    
    resetForm() {
      const formMainTitle = document.getElementById('form-main-title');
      const form = document.getElementById('product-form');
      const newCategoryGroup = document.getElementById('new-category-group');

      if (formMainTitle) formMainTitle.textContent = 'Add New Product';
      if (form) form.reset();
      if (newCategoryGroup) newCategoryGroup.style.display = 'none';

      // Clear key features
      this.keyPoints = [];
      this.renderKeyFeatures();

      // Re-render form to get latest categories
      this.renderForm();
    }
    
    async saveProduct() {
      const form = document.getElementById('product-form');
      if (!form) {
        this.showNotification('Form not found', 'error');
        return;
      }

      // Get form values directly from inputs
      const nameInput = document.getElementById('product-name');
      const categoryInput = document.getElementById('product-category');
      const priceInput = document.getElementById('product-price');
      const descriptionInput = document.getElementById('product-description');
      const imagesInput = document.getElementById('product-images');
      const defectsInput = document.getElementById('product-defects');

      if (!nameInput || !categoryInput || !priceInput || !descriptionInput) {
        this.showNotification('Required form fields not found', 'error');
        return;
      }

      // Handle category selection
      let category = categoryInput.value.trim();

      if (category === '__new__') {
        const newCategoryInput = document.getElementById('new-category-name');
        if (!newCategoryInput || !newCategoryInput.value.trim()) {
          this.showNotification('Please enter a new category name', 'error');
          return;
        }
        category = newCategoryInput.value.trim();
      }

      // Validate required fields
      const name = nameInput.value.trim();
      const price = parseFloat(priceInput.value);
      const description = descriptionInput.value.trim();

      if (!name || !category || !description || isNaN(price) || price <= 0) {
        this.showNotification('Please fill in all required fields with valid values', 'error');
        return;
      }

      // Process images
      const imagesText = imagesInput ? imagesInput.value.trim() : '';
      const images = imagesText ? imagesText.split('\n').map(img => img.trim()).filter(img => img) : [];

      // Generate SEO-optimized metadata
      const slug = this.generateSlug(name);
      const seoTitle = `${name} - $${parseFloat(price).toFixed(2)} | Cheers Marketplace - Chico, CA`;
      const seoDescription = `${description.length > 120 ? description.substring(0, 117) + '...' : description} Family-run business in Chico, CA. Gently used, personally inspected.`;
      const seoKeywords = [
        name.toLowerCase(),
        category.toLowerCase(),
        'cheap used goods',
        'affordable secondhand',
        'budget items',
        'quality thrift',
        'gently used',
        'Chico CA',
        'family business',
        'personally inspected',
        'discount goods',
        'bargain finds',
        'low cost',
        'inexpensive',
        'value shopping',
        ...this.keyPoints.map(kp => kp.value.toLowerCase())
      ].join(', ');

      const product = {
        id: this.editingProduct?.id || Date.now().toString(),
        name: name,
        category: category,
        price: price,
        description: description,
        images: images,
        keyPoints: [...this.keyPoints],
        defects: defectsInput ? defectsInput.value.trim() || null : null,
        createdAt: this.editingProduct?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        slug: slug,
        // SEO metadata
        seo: {
          title: seoTitle,
          description: seoDescription,
          keywords: seoKeywords,
          canonicalUrl: `https://www.cheersmarketplace.com/products/${slug}`,
          ogImage: images[0] || null,
          schema: {
            "@context": "https://schema.org",
            "@type": "Product",
            "name": name,
            "description": description,
            "image": images,
            "category": category,
            "brand": {
              "@type": "Brand",
              "name": "Cheers Marketplace"
            },
            "offers": {
              "@type": "Offer",
              "price": parseFloat(price),
              "priceCurrency": "USD",
              "availability": "https://schema.org/InStock"
            }
          }
        }
      };

      try {
        if (this.editingProduct) {
          // Update existing product
          const index = this.products.findIndex(p => p.id === this.editingProduct.id);
          if (index > -1) {
            this.products[index] = product;
          }
        } else {
          // Add new product
          this.products.push(product);
        }

        // Update localStorage
        localStorage.setItem('cheers_products', JSON.stringify(this.products));

        // Mark as changed (don't auto-sync)
        this.markAsChanged();

        // Re-render and switch to list
        this.filteredProducts = [...this.products];
        this.filterProducts();
        this.switchTab('list');

        this.showNotification(
          this.editingProduct ? 'Product updated successfully!' : 'Product added successfully!',
          'success'
        );

        this.editingProduct = null;
      } catch (error) {
        console.error('Save error:', error);
        this.showNotification('Failed to save product', 'error');
      }
    }

    generateSlug(name) {
      return name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    async syncToServer() {
      try {
        console.log('Syncing products to server...', this.products.length, 'products');

        const response = await fetch('/api/sync-products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            products: this.products
          })
        });

        console.log('Sync response status:', response.status);
        console.log('Sync response headers:', Object.fromEntries(response.headers.entries()));

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Check if response has content
        const responseText = await response.text();
        console.log('Raw response text:', responseText);

        if (!responseText.trim()) {
          throw new Error('Empty response from server');
        }

        // Parse JSON
        let result;
        try {
          result = JSON.parse(responseText);
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
          console.error('Response text that failed to parse:', responseText);
          throw new Error(`Invalid JSON response: ${parseError.message}`);
        }

        console.log('Parsed result:', result);

        if (result.success) {
          console.log('Products synced to server successfully');

          // Show detailed success message
          let message = 'Changes saved successfully!';
          if (result.github?.committed) {
            message += ' Deployment triggered via GitHub.';
          } else if (result.github?.configured === false) {
            message += ' (GitHub not configured - manual deployment needed)';
          } else if (result.github?.error) {
            message += ` (GitHub error: ${result.github.error})`;
          }

          this.showNotification(message, 'success');

          // Mark as synced after successful sync
          this.markAsSynced();
        } else {
          console.error('Server sync failed:', result.error);
          this.showNotification('Failed to sync to server: ' + (result.error || 'Unknown error'), 'error');
        }
      } catch (error) {
        console.error('Sync error:', error);
        this.showNotification('Failed to sync to server: ' + error.message, 'error');
      }
    }

    async triggerBuildHook() {
      try {
        // Get build hook URL from environment or localStorage
        const buildHookUrl = localStorage.getItem('build_hook_url') ||
                            window.location.origin + '/api/build-hook';

        const response = await fetch(buildHookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            trigger: 'product_update',
            timestamp: new Date().toISOString()
          })
        });

        if (response.ok) {
          console.log('Build hook triggered successfully');
        } else {
          console.warn('Build hook failed:', response.status);
        }
      } catch (error) {
        console.warn('Build hook error:', error);
        // Don't show error to user as this is not critical
      }
    }
    
    showNotification(message, type = 'info') {
      // Create notification element
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      
      // Add to page
      document.body.appendChild(notification);
      
      // Remove after 3 seconds
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }
  }
  
  // Initialize admin panel
  window.adminPanel = new ModernAdminPanel();
</script>

<style>
  /* Professional Form Layout */
  .professional-form-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0;
  }

  /* Form Header */
  .form-header-section {
    background: linear-gradient(135deg, #4b3a1e 0%, #6b5b3f 100%);
    border-radius: 20px 20px 0 0;
    padding: 2.5rem 3rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(75, 58, 30, 0.15);
  }

  .form-header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  .form-header-icon {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .form-header-text h2 {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.02em;
  }

  .form-header-text p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-weight: 400;
  }

  /* Form Content Grid */
  .form-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 0 1rem;
  }

  /* Form Cards */
  .form-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
    border: 1px solid #f1f5f9;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #b8860b 0%, #daa520 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .form-card:hover {
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
    border-color: #e2e8f0;
  }

  .form-card:hover::before {
    opacity: 1;
  }

  /* Card Headers */
  .card-header {
    background: linear-gradient(135deg, #fefcf7 0%, #faf8f3 100%);
    padding: 2rem 2.5rem;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
    gap: 1.25rem;
  }

  .card-icon {
    width: 52px;
    height: 52px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .primary-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  .secondary-icon {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  }

  .features-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }

  .notes-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  }

  .card-title h3 {
    font-size: 1.375rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 0.25rem 0;
    letter-spacing: -0.025em;
  }

  .card-title span {
    font-size: 0.95rem;
    color: #64748b;
    font-weight: 500;
  }

  /* Card Content */
  .card-content {
    padding: 2.5rem;
  }

  /* Input Groups */
  .input-group {
    margin-bottom: 2rem;
  }

  .input-group:last-child {
    margin-bottom: 0;
  }

  .input-label {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    line-height: 1.4;
    letter-spacing: -0.01em;
  }

  .required {
    color: #ef4444;
    font-weight: 700;
  }

  /* Form Inputs */
  .form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    line-height: 1.5;
    color: #374151;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
  }

  .form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    background: #fefefe;
  }

  .form-input:hover:not(:focus) {
    border-color: #d1d5db;
  }

  .form-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
  }

  .form-textarea {
    resize: vertical;
    min-height: 120px;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
  }

  .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1rem center;
    background-repeat: no-repeat;
    background-size: 1.25rem;
    padding-right: 3rem;
    appearance: none;
  }

  /* Input Rows */
  .input-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  /* Price Input */
  .price-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  .currency-prefix {
    position: absolute;
    left: 1.25rem;
    color: #6b7280;
    font-weight: 600;
    z-index: 1;
    pointer-events: none;
    font-size: 1rem;
  }

  .price-input {
    padding-left: 2.5rem;
  }

  /* Input Help Text */
  .input-help {
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: #6b7280;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    line-height: 1.5;
    font-weight: 500;
  }

  .warning-help {
    color: #f59e0b;
  }

  .warning-help svg {
    color: #f59e0b;
  }

  .input-help svg {
    margin-top: 0.125rem;
    flex-shrink: 0;
  }

  /* Features Display Area */
  .features-display-area {
    min-height: 100px;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px dashed #cbd5e1;
    border-radius: 16px;
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
    transition: all 0.3s ease;
  }

  .features-display-area:hover {
    border-color: #94a3b8;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  }

  .features-display-area:empty::before {
    content: "No features added yet. Use the form below to add product specifications.";
    color: #64748b;
    font-style: italic;
    font-size: 0.95rem;
    width: 100%;
    text-align: center;
    font-weight: 500;
  }

  .keypoint-item {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    font-size: 0.875rem;
    gap: 0.75rem;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
  }

  .keypoint-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .keypoint-label {
    font-weight: 700;
    letter-spacing: -0.01em;
  }

  .keypoint-value {
    font-weight: 500;
  }

  .btn-remove-keypoint {
    background: rgba(255, 255, 255, 0.25);
    border: none;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 700;
    font-size: 12px;
    line-height: 1;
  }

  .btn-remove-keypoint:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
  }

  /* Features Input Section */
  .features-input-section {
    background: #fafafa;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
  }

  .features-input-section:hover {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  .features-input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .add-feature-button {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    color: white;
    border: none;
    padding: 1rem 1.75rem;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    width: fit-content;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
    letter-spacing: -0.01em;
  }

  .add-feature-button:hover {
    background: linear-gradient(135deg, #047857 0%, #10b981 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
  }

  .add-feature-button:active {
    transform: translateY(0);
  }

  /* Image Upload Area */
  .image-upload-area {
    position: relative;
  }

  .image-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .form-content-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .professional-form-wrapper {
      max-width: 900px;
    }
  }

  @media (max-width: 768px) {
    .professional-form-wrapper {
      padding: 0;
    }

    .form-header-section {
      padding: 2rem 1.5rem;
      border-radius: 16px 16px 0 0;
    }

    .form-header-content {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .form-header-icon {
      width: 56px;
      height: 56px;
    }

    .form-header-text h2 {
      font-size: 1.75rem;
    }

    .form-content-grid {
      padding: 0 0.5rem;
      gap: 1.5rem;
    }

    .card-header {
      padding: 1.5rem 2rem;
    }

    .card-content {
      padding: 2rem;
    }

    .card-icon {
      width: 44px;
      height: 44px;
    }

    .card-title h3 {
      font-size: 1.25rem;
    }

    .input-row {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .features-input-grid {
      grid-template-columns: 1fr;
      gap: 1.25rem;
    }
  }

  @media (max-width: 480px) {
    .form-header-section {
      padding: 1.5rem 1rem;
    }

    .form-header-text h2 {
      font-size: 1.5rem;
    }

    .form-header-text p {
      font-size: 1rem;
    }

    .card-header {
      padding: 1.25rem 1.5rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .card-content {
      padding: 1.5rem;
    }

    .card-icon {
      width: 40px;
      height: 40px;
    }

    .form-input {
      font-size: 16px; /* Prevents zoom on iOS */
    }
  }

  @media (max-width: 480px) {
    .section-header {
      padding: 0.875rem 1rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .section-content {
      padding: 1rem;
    }

    .field-input {
      font-size: 16px; /* Prevents zoom on iOS */
    }

    .section-icon {
      width: 32px;
      height: 32px;
    }

    .section-title h3 {
      font-size: 0.9375rem;
    }

    .section-title p {
      font-size: 0.8125rem;
    }
  }

  /* Focus and Interaction States */
  .form-section:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Loading and Disabled States */
  .field-input:disabled {
    background-color: #f3f4f6;
    color: #6b7280;
    cursor: not-allowed;
  }

  /* Improved Select Styling */
  select.field-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.25rem;
    padding-right: 2.5rem;
    appearance: none;
  }

  /* Smooth Animations */
  * {
    transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
  }

  /* Professional Notification Styles */
  .notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1000;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 400px;
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .notification::before {
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .notification-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(22, 163, 74, 0.95) 100%);
    color: white;
    border-color: rgba(34, 197, 94, 0.3);
  }

  .notification-success::before {
    background: rgba(255, 255, 255, 0.3);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
    background-size: 12px;
    background-repeat: no-repeat;
    background-position: center;
  }

  .notification-error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 0.95) 100%);
    color: white;
    border-color: rgba(239, 68, 68, 0.3);
  }

  .notification-error::before {
    background: rgba(255, 255, 255, 0.3);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E");
    background-size: 12px;
    background-repeat: no-repeat;
    background-position: center;
  }

  /* GitHub Status Styles */
  .admin-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .github-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: var(--border-light);
    border-radius: var(--radius);
    font-size: 0.875rem;
    color: var(--text-secondary);
    border: 1px solid var(--border);
  }

  .status-indicator {
    font-size: 0.75rem;
    cursor: help;
  }

  .status-text {
    font-weight: 500;
    white-space: nowrap;
  }

  /* Sync button with changes indicator */
  .btn-sync.has-changes {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    animation: pulse-changes 2s infinite;
  }

  .btn-sync.has-changes:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
  }

  @keyframes pulse-changes {
    0%, 100% {
      box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    }
    50% {
      box-shadow: 0 2px 12px rgba(245, 158, 11, 0.5);
    }
  }

  /* Product change indicators */
  .product-row.product-new {
    background: rgba(34, 197, 94, 0.05);
    border-left: 3px solid #22c55e;
  }

  .product-row.product-modified {
    background: rgba(245, 158, 11, 0.05);
    border-left: 3px solid #f59e0b;
  }

  .change-indicator {
    display: inline-block;
    margin-right: 0.5rem;
    font-size: 0.875rem;
    font-weight: bold;
    cursor: help;
  }

  .change-indicator.new {
    color: #22c55e;
  }

  .change-indicator.modified {
    color: #f59e0b;
  }

  .product-name-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .notification-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(37, 99, 235, 0.95) 100%);
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
  }

  .notification-info::before {
    background: rgba(255, 255, 255, 0.3);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z'/%3E%3C/svg%3E");
    background-size: 12px;
    background-repeat: no-repeat;
    background-position: center;
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @media (max-width: 768px) {
    .notification {
      top: 1rem;
      right: 1rem;
      left: 1rem;
      max-width: none;
    }
  }
</style>
