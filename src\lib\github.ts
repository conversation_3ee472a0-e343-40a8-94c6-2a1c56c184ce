// GitHub API integration for automatic commits
export interface GitHubConfig {
  owner: string;
  repo: string;
  token: string;
  branch?: string;
}

export interface CommitOptions {
  message: string;
  filePath: string;
  content: string;
  branch?: string;
}

export class GitHubService {
  private config: GitHubConfig;
  private baseUrl = 'https://api.github.com';

  constructor(config: GitHubConfig) {
    this.config = {
      ...config,
      branch: config.branch || 'main'
    };
  }

  /**
   * Get the current SHA of a file
   */
  private async getFileSha(filePath: string): Promise<string | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}/contents/${filePath}?ref=${this.config.branch}`,
        {
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Cheers-Marketplace-Admin'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        return data.sha;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting file SHA:', error);
      return null;
    }
  }

  /**
   * Commit and push a file to GitHub
   */
  async commitFile(options: CommitOptions): Promise<{ success: boolean; error?: string; commitSha?: string }> {
    try {
      const { message, filePath, content, branch = this.config.branch } = options;
      
      // Get current file SHA (needed for updates)
      const currentSha = await this.getFileSha(filePath);
      
      // Prepare the commit data
      // Use btoa instead of Buffer for Cloudflare Pages compatibility
      const commitData: any = {
        message,
        content: btoa(content), // btoa is available in Cloudflare Workers/Pages
        branch
      };

      // Include SHA if file exists (for updates)
      if (currentSha) {
        commitData.sha = currentSha;
      }

      // Make the commit
      const response = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}/contents/${filePath}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json',
            'User-Agent': 'Cheers-Marketplace-Admin'
          },
          body: JSON.stringify(commitData)
        }
      );

      if (response.ok) {
        const result = await response.json();
        return {
          success: true,
          commitSha: result.commit?.sha
        };
      } else {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: `GitHub API error: ${response.status} - ${errorData.message || response.statusText}`
        };
      }
    } catch (error) {
      console.error('GitHub commit error:', error);
      return {
        success: false,
        error: `Failed to commit to GitHub: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * Commit products.json file specifically
   */
  async commitProducts(products: any[]): Promise<{ success: boolean; error?: string; commitSha?: string }> {
    const content = JSON.stringify(products, null, 2);
    const timestamp = new Date().toISOString();
    
    return this.commitFile({
      message: `Update products.json - ${products.length} products (${timestamp})`,
      filePath: 'src/data/products.json',
      content
    });
  }

  /**
   * Test GitHub connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string; repoInfo?: any }> {
    try {
      const response = await fetch(
        `${this.baseUrl}/repos/${this.config.owner}/${this.config.repo}`,
        {
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Cheers-Marketplace-Admin'
          }
        }
      );

      if (response.ok) {
        const repoInfo = await response.json();
        return {
          success: true,
          repoInfo: {
            name: repoInfo.name,
            fullName: repoInfo.full_name,
            defaultBranch: repoInfo.default_branch,
            private: repoInfo.private
          }
        };
      } else {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: `GitHub API error: ${response.status} - ${errorData.message || response.statusText}`
        };
      }
    } catch (error) {
      console.error('GitHub connection test error:', error);
      return {
        success: false,
        error: `Failed to connect to GitHub: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
}

/**
 * Create GitHub service instance from environment variables
 * In Cloudflare Pages, environment variables are passed via the context
 */
export function createGitHubService(env?: any): GitHubService | null {
  // Try multiple ways to access environment variables
  const owner = env?.GITHUB_OWNER || import.meta.env.GITHUB_OWNER || process.env.GITHUB_OWNER;
  const repo = env?.GITHUB_REPO || import.meta.env.GITHUB_REPO || process.env.GITHUB_REPO;
  const token = env?.GITHUB_TOKEN || import.meta.env.GITHUB_TOKEN || process.env.GITHUB_TOKEN;
  const branch = env?.GITHUB_BRANCH || import.meta.env.GITHUB_BRANCH || process.env.GITHUB_BRANCH || 'main';

  console.log('GitHub config check:', {
    owner: owner ? '***' : 'missing',
    repo: repo ? '***' : 'missing',
    token: token ? '***' : 'missing',
    branch: branch || 'main'
  });

  if (!owner || !repo || !token) {
    console.warn('GitHub configuration missing. Required: GITHUB_OWNER, GITHUB_REPO, GITHUB_TOKEN');
    console.warn('Available env keys:', env ? Object.keys(env) : 'no env object');
    return null;
  }

  return new GitHubService({ owner, repo, token, branch });
}
