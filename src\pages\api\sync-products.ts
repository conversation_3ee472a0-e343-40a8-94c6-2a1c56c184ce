import type { APIRoute } from 'astro';
import { createGitHubService } from '../../lib/github';
import { generateSlug } from '../../lib/products';

// Import products data directly since we can't write to filesystem in Cloudflare Pages
import productsData from '../../data/products.json';

export const GET: APIRoute = async () => {
  try {
    console.log('Sync products GET called');

    // Use imported products data since we can't read files in Cloudflare Pages
    const products = productsData || [];

    console.log(`Found ${products.length} products`);

    return new Response(JSON.stringify({
      success: true,
      products,
      count: products.length,
      lastModified: new Date().toISOString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Failed to read products:', error);

    return new Response(JSON.stringify({
      success: false,
      error: `Failed to read products: ${error instanceof Error ? error.message : String(error)}`,
      stack: error instanceof Error ? error.stack : undefined
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const POST: APIRoute = async ({ request, locals }) => {
  console.log('Sync products API called');
  console.log('Environment variables available:', (locals as any)?.runtime?.env ? Object.keys((locals as any).runtime.env) : 'no env');

  try {
    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid JSON in request body'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('Request body parsed, products count:', body.products?.length);

    if (!body.products || !Array.isArray(body.products)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid products data - expected array'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate each product
    const validatedProducts = body.products.map((product: any, index: number) => {
      const errors = [];
      
      if (!product.name || typeof product.name !== 'string') {
        errors.push(`Product ${index}: name is required`);
      }
      
      if (!product.category || typeof product.category !== 'string') {
        errors.push(`Product ${index}: category is required`);
      }
      
      if (!product.description || typeof product.description !== 'string') {
        errors.push(`Product ${index}: description is required`);
      }
      
      if (typeof product.price !== 'number' || product.price <= 0) {
        errors.push(`Product ${index}: price must be a positive number`);
      }
      
      if (errors.length > 0) {
        throw new Error(errors.join(', '));
      }
      
      // Ensure required fields and generate slug if missing
      return {
        id: product.id || Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: product.name.trim(),
        category: product.category.trim(),
        description: product.description.trim(),
        price: Number(product.price),
        images: Array.isArray(product.images) ? product.images.filter((img: any) => img && img.trim()) : [],
        keyPoints: Array.isArray(product.keyPoints) ? product.keyPoints : [],
        defects: product.defects || null,
        slug: product.slug || generateSlug(product.name),
        createdAt: product.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    });
    
    // Note: In Cloudflare Pages, we can't write to the filesystem
    // The products will only be persisted via GitHub commit
    console.log(`Validated ${validatedProducts.length} products for sync`);

    // Commit to GitHub (this will trigger Cloudflare Pages build automatically)
    const githubService = createGitHubService((locals as any)?.runtime?.env);
    let githubCommitResult = null;

    if (githubService) {
      try {
        console.log('Committing products to GitHub...');
        githubCommitResult = await githubService.commitProducts(validatedProducts);

        if (githubCommitResult.success) {
          console.log(`Successfully committed products to GitHub. Commit SHA: ${githubCommitResult.commitSha}`);
        } else {
          console.error('Failed to commit to GitHub:', githubCommitResult.error);
        }
      } catch (githubError) {
        console.error('GitHub commit error:', githubError);
        githubCommitResult = { success: false, error: githubError instanceof Error ? githubError.message : String(githubError) };
      }
    } else {
      console.warn('GitHub service not configured. Skipping GitHub commit.');
    }

    // Fallback: Trigger build hook directly if GitHub commit failed
    if (!githubCommitResult?.success) {
      try {
        const buildHookUrl = (locals as any)?.runtime?.env?.CLOUDFLARE_BUILD_HOOK_URL;
        if (buildHookUrl) {
          console.log('GitHub commit failed, trying direct build hook...');
          await fetch(buildHookUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              source: 'product-sync-fallback',
              trigger: 'products_updated',
              timestamp: new Date().toISOString()
            })
          });
          console.log('Direct build hook triggered after GitHub failure');
        }
      } catch (buildError) {
        console.warn('Failed to trigger fallback build hook:', buildError);
      }
    }
    
    const responseData = {
      success: true,
      message: `Successfully processed ${validatedProducts.length} products`,
      count: validatedProducts.length,
      environment: 'cloudflare-pages',
      github: githubCommitResult ? {
        committed: githubCommitResult.success,
        commitSha: githubCommitResult.commitSha,
        error: githubCommitResult.error
      } : { configured: false },
      note: 'Products are persisted via GitHub commit only (no local file system in Cloudflare Pages)'
    };

    console.log('Sending success response:', responseData);

    return new Response(JSON.stringify(responseData), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Product sync error:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack available');

    const errorResponse = {
      success: false,
      error: error instanceof Error ? error.message : String(error) || 'Failed to sync products',
      details: error instanceof Error && error.stack ? error.stack.split('\n').slice(0, 3).join('\n') : 'No stack trace'
    };

    console.log('Sending error response:', errorResponse);

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
