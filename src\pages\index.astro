---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import '../assets/global.css';
---

<Layout>
  <Header />
  <main>
	<section class="homepage-card">
	  <h1>Welcome to Cheers Marketplace</h1>
	  <p>Your destination for affordable, quality used goods in Chico, CA. Our family-run business specializes in cheap secondhand items, thrift finds, and budget-friendly pre-owned products. Every item is personally inspected by our husband-and-wife team to ensure you get the best value for your money.</p>
	  <a href="/products" class="cta-btn">Browse Products</a>
	</section>
  </main>
  <Footer />
</Layout>
<style>
  .homepage-card {
	background: var(--card-bg);
	border-radius: var(--radius-xl);
	box-shadow: var(--shadow-xl);
	border: 1px solid var(--border);
	padding: 4rem 3rem;
	margin: 3rem 0 2rem 0;
	max-width: 700px;
	width: 100%;
	text-align: center;
  }
  h1 {
	font-family: Georgia, 'Times New Roman', Times, serif;
	color: var(--text);
	font-size: 3rem;
	font-weight: 600;
	margin-top: 0;
	margin-bottom: 1.5rem;
	letter-spacing: -0.025em;
	line-height: 1.1;
  }
  .homepage-card p {
	font-size: 1.125rem;
	color: var(--text-secondary);
	margin-bottom: 2.5rem;
	line-height: 1.7;
	max-width: 500px;
	margin-left: auto;
	margin-right: auto;
  }
  .cta-btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	background: var(--primary);
	color: white;
	font-weight: 600;
	font-size: 1rem;
	padding: 1rem 2rem;
	border-radius: var(--radius);
	box-shadow: var(--shadow-lg);
	border: none;
	transition: all 0.2s ease;
	text-decoration: none;
	letter-spacing: -0.01em;
  }
  .cta-btn:hover {
	background: var(--primary-dark);
	color: white;
	box-shadow: var(--shadow-xl);
	transform: translateY(-2px);
	text-decoration: none;
  }

  @media (max-width: 768px) {
	.homepage-card {
	  padding: 2.5rem 2rem;
	  margin: 2rem 0;
	}
	h1 {
	  font-size: 2.25rem;
	}
	.homepage-card p {
	  font-size: 1rem;
	  margin-bottom: 2rem;
	}
	.cta-btn {
	  padding: 0.875rem 1.75rem;
	  font-size: 0.95rem;
	}
  }
</style>
