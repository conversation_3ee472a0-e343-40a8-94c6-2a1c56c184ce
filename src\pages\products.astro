---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import OptimizedProductsList from '../components/OptimizedProductsList.astro';
import { generateSlug } from '../lib/products';
import '../assets/global.css';

// Import products data directly for maximum performance
import products from '../data/products.json';

// Generate categories and price range at build time
const categories = [...new Set(products.map(p => p.category))].sort();
const prices = products.map(p => Number(p.price || 0));
const priceRange = {
  min: Math.min(...prices),
  max: Math.max(...prices)
};

// Generate structured data for the products page
const structuredData = {
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": "Products - Cheers Marketplace",
  "description": "Discover unique, curated products from passionate creators around the world.",
  "url": Astro.url.href,
  "mainEntity": {
    "@type": "ItemList",
    "numberOfItems": products.length,
    "itemListElement": products.slice(0, 12).map((product, index) => ({
      "@type": "Product",
      "position": index + 1,
      "name": product.name,
      "description": product.description,
      "image": product.images?.[0],
      "url": `/products/${generateSlug(product.name)}`,
      "offers": {
        "@type": "Offer",
        "price": product.price,
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      }
    }))
  }
};
---

<Layout>
  <Fragment slot="head">
    <title>Products | Cheers Marketplace</title>
    <meta name="description" content={`Discover unique, curated products from passionate creators around the world. Browse our collection of ${products.length} handpicked items.`} />
    <meta property="og:title" content="Products | Cheers Marketplace" />
    <meta property="og:description" content="Discover unique, curated products from passionate creators around the world." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <script type="application/ld+json" set:html={JSON.stringify(structuredData)} is:inline></script>

    <!-- Critical CSS for products page mobile performance -->
    <style is:inline>
      /* Critical above-the-fold styles for mobile LCP */
      .products-page-container {
        padding-top: 1rem;
      }
      .products-header {
        margin-bottom: 1.5rem;
      }
      .products-header h1 {
        font-size: 1.75rem;
        margin: 0 0 0.5rem 0;
        font-weight: 600;
        color: var(--text);
      }
      .products-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
      }
      @media (max-width: 768px) {
        .products-page-container {
          padding-top: 0.5rem;
        }
        .products-header h1 {
          font-size: 1.5rem;
        }
        .products-controls {
          gap: 0.75rem;
          margin-bottom: 1rem;
        }
      }
    </style>

    <!-- Font preloading is now handled in Layout.astro for optimal performance -->
  </Fragment>

  <Header />

  <main class="products-main">
    <!-- Enhanced Hero Section -->
    <section class="products-hero-section">
      <div class="container">
        <div class="products-hero">
          <div class="hero-content">
            <h1>Our Products</h1>
            <p class="hero-subtitle">Discover unique, quality items carefully curated for you</p>
          </div>
          <div class="hero-stats">
            <div class="stat-card">
              <div class="stat-number">{products.length}</div>
              <div class="stat-label">Products</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{categories.length}</div>
              <div class="stat-label">Categories</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${priceRange.min.toFixed(0)} - ${priceRange.max.toFixed(0)}</div>
              <div class="stat-label">Price Range</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Enhanced Filters Section -->
    <section class="filters-section">
      <div class="container">
        <div class="filters-container">
          <div class="filters-bar">
            <div class="filters-left">
              <select id="category-filter" class="filter-select" aria-label="Filter products by category">
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option value={category}>{category}</option>
                ))}
              </select>

              <select id="sort-by" class="filter-select" aria-label="Sort products by different criteria">
                <option value="name">Name A-Z</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="newest">Newest First</option>
              </select>
            </div>

            <div class="filters-right">
              <div class="search-container">
                <input
                  id="product-search"
                  type="search"
                  placeholder="Search products..."
                  class="search-input"
                  autocomplete="off"
                  spellcheck="false"
                  aria-label="Search products by name, description, or category"
                />
                <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
              </div>

              <div class="results-info">
                <span id="results-count">{products.length} products</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
      <div class="container">
        <OptimizedProductsList products={products} />
      </div>
    </section>

  </main>

  <Footer />
</Layout>

<style>
  .products-main {
    padding-top: 0;
    min-height: auto;
  }

  /* Enhanced Hero Section */
  .products-hero-section {
    background: linear-gradient(135deg, var(--light-background) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-bottom: 1px solid var(--border);
    padding: 3rem 0;
    position: relative;
    overflow: hidden;
  }

  .products-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(37, 99, 235, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  .products-hero {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
  }

  .hero-content h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--text);
    letter-spacing: -0.02em;
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
  }

  .hero-stats {
    display: flex;
    gap: 1.5rem;
    align-items: center;
  }

  .stat-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: 1.5rem 1.25rem;
    text-align: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    min-width: 120px;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.25rem;
    line-height: 1;
  }

  .stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  /* Enhanced Filters Section */
  .filters-section {
    padding: 1.5rem 0;
    background: var(--light-background);
    border-bottom: 1px solid var(--border);
  }

  .filters-container {
    background: white;
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border);
  }

  .filters-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
  }

  .filters-left {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .filters-right {
    display: flex;
    gap: 1.5rem;
    align-items: center;
  }

  .filter-select {
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    background: var(--light-background);
    color: var(--text);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 160px;
  }

  .filter-select:hover {
    border-color: var(--primary);
    background: white;
  }

  .filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: white;
  }

  .search-container {
    position: relative;
    min-width: 280px;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.75rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    background: var(--light-background);
    color: var(--text);
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .search-input:hover {
    border-color: var(--primary);
    background: white;
  }

  .search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: white;
  }

  .search-input::placeholder {
    color: var(--muted);
  }

  .search-icon {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
  }

  .results-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 600;
    white-space: nowrap;
    padding: 0.75rem 1rem;
    background: var(--border-light);
    border-radius: var(--radius);
    border: 1px solid var(--border);
  }

  /* Products Section */
  .products-section {
    padding: 3rem 0;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .products-hero-section {
      padding: 2rem 0;
    }

    .products-hero {
      flex-direction: column;
      align-items: flex-start;
      gap: 2rem;
    }

    .hero-content h1 {
      font-size: 2.5rem;
    }

    .hero-subtitle {
      font-size: 1rem;
    }

    .hero-stats {
      flex-wrap: wrap;
      gap: 1rem;
      width: 100%;
      justify-content: center;
    }

    .stat-card {
      flex: 1;
      min-width: 100px;
      padding: 1.25rem 1rem;
    }

    .stat-number {
      font-size: 1.25rem;
    }

    .stat-label {
      font-size: 0.75rem;
    }

    .filters-section {
      padding: 1rem 0;
    }

    .filters-container {
      padding: 1.25rem;
    }

    .filters-bar {
      flex-direction: column;
      gap: 1.5rem;
      align-items: stretch;
    }

    .filters-left {
      flex-direction: column;
      gap: 1rem;
    }

    .filters-right {
      flex-direction: column;
      gap: 1rem;
    }

    .filter-select {
      min-width: auto;
    }

    .search-container {
      min-width: auto;
    }

    .results-info {
      text-align: center;
    }

    .products-section {
      padding: 2rem 0;
    }
  }

  @media (max-width: 480px) {
    .products-hero-section {
      padding: 1.5rem 0;
    }

    .hero-content h1 {
      font-size: 2rem;
    }

    .hero-subtitle {
      font-size: 0.875rem;
    }

    .hero-stats {
      gap: 0.75rem;
    }

    .stat-card {
      padding: 1rem 0.75rem;
      min-width: 80px;
    }

    .stat-number {
      font-size: 1.125rem;
    }

    .stat-label {
      font-size: 0.6875rem;
    }

    .filters-container {
      padding: 1rem;
    }

    .filters-bar {
      gap: 1rem;
    }

    .filters-left {
      gap: 0.75rem;
    }

    .filters-right {
      gap: 0.75rem;
    }

    .filter-select,
    .search-input {
      padding: 0.75rem;
      font-size: 1rem;
    }

    .search-input {
      padding-left: 2.5rem;
    }

    .search-icon {
      left: 0.75rem;
      width: 18px;
      height: 18px;
    }

    .products-section {
      padding: 1.5rem 0;
    }
  }
</style>
