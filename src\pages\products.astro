---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import OptimizedProductsList from '../components/OptimizedProductsList.astro';
import { generateSlug } from '../lib/products';
import '../assets/global.css';

// Import products data directly for maximum performance
import products from '../data/products.json';

// Generate categories and price range at build time
const categories = [...new Set(products.map(p => p.category))].sort();
const prices = products.map(p => Number(p.price || 0));
const priceRange = {
  min: Math.min(...prices),
  max: Math.max(...prices)
};

// Generate structured data for the products page
const structuredData = {
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": "Products - Cheers Marketplace",
  "description": "Discover unique, curated products from passionate creators around the world.",
  "url": Astro.url.href,
  "mainEntity": {
    "@type": "ItemList",
    "numberOfItems": products.length,
    "itemListElement": products.slice(0, 12).map((product, index) => ({
      "@type": "Product",
      "position": index + 1,
      "name": product.name,
      "description": product.description,
      "image": product.images?.[0],
      "url": `/products/${generateSlug(product.name)}`,
      "offers": {
        "@type": "Offer",
        "price": product.price,
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      }
    }))
  }
};
---

<Layout>
  <Fragment slot="head">
    <title>Products | Cheers Marketplace</title>
    <meta name="description" content={`Discover unique, curated products from passionate creators around the world. Browse our collection of ${products.length} handpicked items.`} />
    <meta property="og:title" content="Products | Cheers Marketplace" />
    <meta property="og:description" content="Discover unique, curated products from passionate creators around the world." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <script type="application/ld+json" set:html={JSON.stringify(structuredData)} is:inline></script>

    <!-- Critical CSS for products page performance and layout shift prevention -->
    <style is:inline>
      /* Critical above-the-fold styles to prevent layout shift */
      .products-main {
        min-height: 100vh;
      }
      .products-header-section {
        background: white;
        border-bottom: 1px solid #e2e8f0;
        padding: 1.5rem 0;
        min-height: 120px;
      }
      .products-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1200px;
        margin: 0 auto;
        gap: 2rem;
        min-height: 80px;
      }
      .header-title h1 {
        font-family: 'Playfair Display', serif;
        font-size: 2rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        color: #0f172a;
        letter-spacing: -0.025em;
      }
      .products-section {
        padding: 2rem 0;
        min-height: 600px;
      }
      /* Prevent layout shift on mobile */
      @media (max-width: 768px) {
        .products-header-section {
          min-height: 160px;
        }
        .products-header {
          flex-direction: column;
          align-items: stretch;
          gap: 1.5rem;
          min-height: 120px;
        }
        .header-title {
          text-align: center;
        }
        .header-title h1 {
          font-size: 1.75rem;
        }
      }
    </style>

    <!-- Font preloading is now handled in Layout.astro for optimal performance -->
  </Fragment>

  <Header />

  <main class="products-main">
    <!-- Compact Products Header -->
    <section class="products-header-section">
      <div class="container">
        <div class="products-header">
          <div class="header-title">
            <h1>Our Products</h1>
            <div class="header-stats">
              <span>{products.length} Products</span>
              <span>•</span>
              <span>{categories.length} Categories</span>
              <span>•</span>
              <span>${priceRange.min.toFixed(0)} - ${priceRange.max.toFixed(0)}</span>
            </div>
          </div>

          <div class="header-controls">
            <select id="category-filter" class="control-select" aria-label="Filter products by category">
              <option value="">All Categories</option>
              {categories.map(category => (
                <option value={category}>{category}</option>
              ))}
            </select>

            <select id="sort-by" class="control-select" aria-label="Sort products by different criteria">
              <option value="name">Name A-Z</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="newest">Newest First</option>
            </select>

            <div class="search-container">
              <input
                id="product-search"
                type="search"
                placeholder="Search products..."
                class="search-input"
                autocomplete="off"
                spellcheck="false"
                aria-label="Search products by name, description, or category"
              />
              <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
            </div>

            <div class="results-count">
              <span id="results-count">{products.length} products</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
      <div class="container">
        <OptimizedProductsList products={products} />
      </div>
    </section>

  </main>

  <Footer />
</Layout>

<style>
  .products-main {
    padding-top: 0;
    min-height: auto;
  }

  /* Compact Products Header */
  .products-header-section {
    background: white;
    border-bottom: 1px solid var(--border);
    padding: 1.5rem 0;
  }

  .products-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    gap: 2rem;
  }

  .header-title h1 {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: var(--text);
    letter-spacing: -0.025em;
  }

  .header-stats {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .control-select {
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    background: var(--light-background);
    color: var(--text);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;
  }

  .control-select:hover {
    border-color: var(--primary);
    background: white;
  }

  .control-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
    background: white;
  }

  .search-container {
    position: relative;
    min-width: 200px;
  }

  .search-input {
    width: 100%;
    padding: 0.5rem 0.75rem 0.5rem 2rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    background: var(--light-background);
    color: var(--text);
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .search-input:hover {
    border-color: var(--primary);
    background: white;
  }

  .search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
    background: white;
  }

  .search-input::placeholder {
    color: var(--muted);
  }

  .search-icon {
    position: absolute;
    left: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
  }

  .results-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 600;
    white-space: nowrap;
    padding: 0.5rem 0.75rem;
    background: var(--border-light);
    border-radius: var(--radius);
    border: 1px solid var(--border);
  }

  /* Products Section */
  .products-section {
    padding: 2rem 0;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .products-header {
      flex-direction: column;
      align-items: stretch;
      gap: 1.5rem;
    }

    .header-title {
      text-align: center;
    }

    .header-title h1 {
      font-size: 1.75rem;
    }

    .header-stats {
      justify-content: center;
      flex-wrap: wrap;
    }

    .header-controls {
      flex-direction: column;
      gap: 1rem;
    }

    .control-select {
      min-width: auto;
    }

    .search-container {
      min-width: auto;
    }

    .results-count {
      text-align: center;
    }

    .products-section {
      padding: 1.5rem 0;
    }
  }

  @media (max-width: 480px) {
    .products-header-section {
      padding: 1rem 0;
    }

    .header-title h1 {
      font-size: 1.5rem;
    }

    .header-stats {
      font-size: 0.75rem;
      gap: 0.375rem;
    }

    .header-controls {
      gap: 0.75rem;
    }

    .control-select,
    .search-input {
      padding: 0.625rem;
      font-size: 0.875rem;
    }

    .search-input {
      padding-left: 1.875rem;
    }

    .search-icon {
      left: 0.5rem;
      width: 14px;
      height: 14px;
    }

    .results-count {
      padding: 0.5rem;
      font-size: 0.75rem;
    }

    .products-section {
      padding: 1rem 0;
    }
  }
</style>
