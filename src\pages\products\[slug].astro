---
export const prerender = true;

import Layout from '../../layouts/Layout.astro';
import Header from '../../components/Header.astro';
import Footer from '../../components/Footer.astro';
import { generateSlug } from '../../lib/products';
import '../../assets/global.css';

// Import products data directly for static generation
import products from '../../data/products.json';

export async function getStaticPaths() {
  return products.map((product) => ({
    params: { slug: generateSlug(product.name) },
    props: { product },
  }));
}

const { product } = Astro.props;

if (!product) {
  return Astro.redirect('/products');
}

// Generate comprehensive structured data for SEO
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Product",
  "name": product.name,
  "description": product.description,
  "image": product.images,
  "category": product.category,
  "brand": {
    "@type": "Brand",
    "name": "Cheers Marketplace"
  },
  "sku": product.id || generateSlug(product.name),
  "mpn": product.id || generateSlug(product.name),
  "offers": {
    "@type": "Offer",
    "price": product.price,
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock",
    "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    "seller": {
      "@type": "Organization",
      "name": "Cheers Marketplace",
      "url": "https://www.cheersmarketplace.com"
    }
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.5",
    "reviewCount": "1"
  }
};

// Generate breadcrumb structured data
const breadcrumbData = {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://www.cheersmarketplace.com/"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Products",
      "item": "https://www.cheersmarketplace.com/products"
    },
    {
      "@type": "ListItem",
      "position": 3,
      "name": product.name,
      "item": `https://www.cheersmarketplace.com/products/${generateSlug(product.name)}`
    }
  ]
};
---

<Layout>
  <Fragment slot="head">
    <!-- Enhanced SEO Meta Tags -->
    <title>{product.name} - ${Number(product.price).toFixed(2)} | Cheers Marketplace</title>
    <meta name="description" content={`${product.description} - Only $${Number(product.price).toFixed(2)} at Cheers Marketplace. ${product.category} category.`} />
    <meta name="keywords" content={`${product.name}, ${product.category}, marketplace, ${product.keyPoints?.map(kp => kp.value).join(', ') || ''}`} />
    <link rel="canonical" href={`https://www.cheersmarketplace.com/products/${generateSlug(product.name)}`} />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content={`${product.name} - $${Number(product.price).toFixed(2)}`} />
    <meta property="og:description" content={product.description} />
    <meta property="og:image" content={product.images?.[0]} />
    <meta property="og:image:width" content="600" />
    <meta property="og:image:height" content="600" />
    <meta property="og:type" content="product" />
    <meta property="og:url" content={`https://www.cheersmarketplace.com/products/${generateSlug(product.name)}`} />
    <meta property="product:price:amount" content={product.price.toString()} />
    <meta property="product:price:currency" content="USD" />
    <meta property="product:availability" content="in stock" />
    <meta property="product:category" content={product.category} />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={`${product.name} - $${Number(product.price).toFixed(2)}`} />
    <meta name="twitter:description" content={product.description} />
    <meta name="twitter:image" content={product.images?.[0]} />

    <!-- Preload critical resources -->
    {product.images?.[0] && (
      <link rel="preload" as="image" href={product.images[0]} fetchpriority="high" />
    )}

    <!-- Structured Data -->
    <script type="application/ld+json" set:html={JSON.stringify(structuredData)} is:inline></script>
    <script type="application/ld+json" set:html={JSON.stringify(breadcrumbData)} is:inline></script>
  </Fragment>

  <Header />
  
  <main class="product-main">
    <!-- Compact Breadcrumb -->
    <nav class="breadcrumb" aria-label="Breadcrumb">
      <div class="container">
        <ol class="breadcrumb-list">
          <li><a href="/">Home</a></li>
          <li><a href="/products">Products</a></li>
          <li><span aria-current="page">{product.name}</span></li>
        </ol>
      </div>
    </nav>

    <!-- Professional Product Layout -->
    <section class="product-section">
      <div class="container">
        <div class="product-grid">
          <!-- Product Images -->
          <div class="product-images">
            <div class="main-image-container">
              <img 
                id="main-image"
                src={product.images?.[0] || 'https://placehold.co/600x600/e2e8f0/64748b?text=No+Image'} 
                alt={product.name}
                class="main-image"
                loading="eager"
                width="600"
                height="600"
              />
              {product.defects && (
                <div class="defects-badge">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                  </svg>
                  Has Issues
                </div>
              )}
            </div>
            
            {product.images && product.images.length > 1 && (
              <div class="image-thumbnails">
                {product.images.map((image, index) => (
                  <button 
                    class={`thumbnail ${index === 0 ? 'active' : ''}`}
                    data-image={image}
                    aria-label={`View image ${index + 1}`}
                  >
                    <img src={image} alt={`${product.name} view ${index + 1}`} loading="lazy" />
                  </button>
                ))}
              </div>
            )}
          </div>

          <!-- Product Information -->
          <div class="product-info">
            <!-- Product Header Section -->
            <div class="product-header">
              <div class="category-badge">{product.category}</div>
              <h1 class="product-title">{product.name}</h1>
              <div class="product-price">
                <span class="price-amount">${Number(product.price).toFixed(2)}</span>
                <span class="price-currency">USD</span>
              </div>
            </div>

            <!-- Product Description Section -->
            <div class="product-section">
              <h2 class="section-title">Description</h2>
              <div class="product-description">
                <p>{product.description}</p>
              </div>
            </div>

            <!-- Important Disclaimers -->
            <div class="product-section disclaimers">
              <h3>Important Information</h3>
              <div class="disclaimer-content">
                <p><strong>Color Accuracy:</strong> While we take great care to ensure accurate product representation in our images, colors may appear slightly different in person due to lighting, camera settings, and display variations.</p>
                {product.category.toLowerCase().includes('clothing') || product.category.toLowerCase().includes('fabric') ? (
                  <p><strong>Pet-Friendly Household:</strong> Please note that we have a cat in our household. Although all clothing and fabric items are thoroughly steam cleaned and washed, those with cat allergies may wish to consider their purchase carefully.</p>
                ) : null}
                <p><strong>Quality Assurance:</strong> As a family-run business in Chico, CA, we personally inspect all gently used items for quality and defects before listing. Any noted defects are clearly described in the product details.</p>
              </div>
            </div>

            <!-- Key Features Section -->
            {product.keyPoints && product.keyPoints.length > 0 && (
              <div class="product-section">
                <h2 class="section-title">Key Features</h2>
                <div class="features-grid">
                  {product.keyPoints.map((feature) => (
                    <div class="feature-card">
                      <div class="feature-label">{feature.label}</div>
                      <div class="feature-value">{feature.value}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <!-- Important Notes Section -->
            {product.defects && (
              <div class="product-section">
                <div class="product-defects">
                  <div class="defects-header">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                    </svg>
                    <h2 class="section-title defects-title">Important Notes</h2>
                  </div>
                  <p class="defects-text">{product.defects}</p>
                </div>
              </div>
            )}

            <!-- Action Buttons Section -->
            <div class="product-section">
              <div class="product-actions">
                <button class="btn-primary btn-large" onclick="window.open('mailto:<EMAIL>?subject=Inquiry about ' + encodeURIComponent('{product.name}'), '_blank')">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,6.89 21.1,6 20,4Z"/>
                  </svg>
                  Contact About This Item
                </button>
                <button class="btn-secondary" onclick="navigator.share ? navigator.share({title: '{product.name}', url: window.location.href}) : navigator.clipboard.writeText(window.location.href)">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.6 20.92,19A2.84,2.84 0 0,0 18,16.08Z"/>
                  </svg>
                  Share
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Back to Products -->
    <section class="back-section">
      <div class="container">
        <a href="/products" class="back-link">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
          </svg>
          Back to All Products
        </a>
      </div>
    </section>
  </main>

  <Footer />
</Layout>

<!-- Minimal JavaScript for image gallery -->
<script is:inline>
  document.addEventListener('DOMContentLoaded', () => {
    const mainImage = document.getElementById('main-image');
    const thumbnails = document.querySelectorAll('.thumbnail');

    thumbnails.forEach(thumbnail => {
      thumbnail.addEventListener('click', () => {
        const newImageSrc = thumbnail.dataset.image;
        if (mainImage && newImageSrc) {
          mainImage.src = newImageSrc;

          // Update active thumbnail
          thumbnails.forEach(t => t.classList.remove('active'));
          thumbnail.classList.add('active');
        }
      });
    });
  });
</script>

<style>
  /* Professional Product Detail Styles */
  .product-main {
    padding-top: 0;
  }

  .breadcrumb {
    background: var(--border-light);
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border);
  }

  .breadcrumb-list {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 0.875rem;
  }

  .breadcrumb-list li:not(:last-child)::after {
    content: '›';
    margin-left: 0.5rem;
    color: var(--muted);
  }

  .breadcrumb-list a {
    color: var(--primary);
    text-decoration: none;
  }

  .breadcrumb-list a:hover {
    text-decoration: underline;
  }

  .breadcrumb-list span[aria-current="page"] {
    color: var(--text-secondary);
  }

  .product-section {
    padding: 3rem 0;
  }

  .product-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
  }

  .product-images {
    position: sticky;
    top: 2rem;
  }

  .main-image-container {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    background: var(--light-background);
    margin-bottom: 1rem;
  }

  .main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .defects-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(245, 158, 11, 0.95);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .image-thumbnails {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
  }

  .thumbnail {
    aspect-ratio: 1;
    border: 2px solid var(--border);
    border-radius: var(--radius);
    overflow: hidden;
    background: none;
    cursor: pointer;
    transition: border-color 0.2s ease;
  }

  .thumbnail:hover,
  .thumbnail.active {
    border-color: var(--primary);
  }

  .thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .product-info {
    padding: 0;
  }

  /* Product Header Styles */
  .product-header {
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 1rem;
  }

  .category-badge {
    display: inline-block;
    background: var(--primary);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    margin-bottom: 1rem;
  }

  .product-title {
    font-family: Georgia, 'Times New Roman', Times, serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }

  .product-price {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
  }

  .price-amount {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--primary);
    letter-spacing: -0.025em;
  }

  .price-currency {
    font-size: 1.125rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  /* Section Styles */
  .product-section {
    margin-bottom: 1rem;
  }

  /* Disclaimers Styles */
  .disclaimers {
    background: var(--border-light);
    padding: 1rem;
    border-radius: var(--radius);
    border: none;
    margin-top: 0.75rem;
  }

  .disclaimers h3 {
    color: var(--text);
    font-size: 1rem;
    margin: 0 0 0.75rem 0;
    font-weight: 600;
  }

  .disclaimer-content p {
    font-size: 0.85rem;
    line-height: 1.5;
    color: var(--text-secondary);
    margin: 0 0 0.5rem 0;
  }

  .disclaimer-content p:last-child {
    margin-bottom: 0;
  }

  .disclaimer-content strong {
    color: var(--text);
    font-weight: 600;
  }

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text);
    margin: 0 0 0.75rem 0;
    font-family: Georgia, 'Times New Roman', Times, serif;
    letter-spacing: -0.025em;
  }

  .product-description p {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin: 0;
  }

  /* Features Grid */
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .feature-card {
    background: var(--light-background);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: 1.25rem;
    transition: all 0.2s ease;
  }

  .feature-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-sm);
  }

  .feature-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
  }

  .feature-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text);
    line-height: 1.4;
  }

  /* Defects Section */
  .product-defects {
    background: linear-gradient(135deg, #fef3cd 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
  }

  .product-defects::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #f59e0b;
  }

  .defects-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .defects-header svg {
    color: #f59e0b;
    flex-shrink: 0;
  }

  .defects-title {
    color: #92400e;
    margin: 0;
    font-size: 1.125rem;
  }

  .defects-text {
    margin: 0;
    color: #92400e;
    line-height: 1.6;
    font-size: 1rem;
  }

  /* Action Buttons */
  .product-actions {
    display: flex;
    gap: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-light);
  }

  .btn-large {
    padding: 1.25rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    letter-spacing: -0.01em;
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    flex: 1;
    justify-content: center;
    box-shadow: var(--shadow);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .btn-secondary {
    background: var(--light-background);
    color: var(--text-secondary);
    border: 1px solid var(--border);
    padding: 1.25rem 1.5rem;
  }

  .btn-secondary:hover {
    background: var(--border-light);
    color: var(--text);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  .back-section {
    padding: 1rem 0 2rem 0;
    border-top: 1px solid var(--border-light);
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
  }

  .back-link:hover {
    color: var(--primary-dark);
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .product-section {
      padding: 2rem 0;
    }

    .product-grid {
      grid-template-columns: 1fr;
      gap: 3rem;
    }

    .product-images {
      position: static;
    }

    .product-title {
      font-size: 2.25rem;
    }

    .price-amount {
      font-size: 2rem;
    }

    .features-grid {
      grid-template-columns: 1fr;
      gap: 1.25rem;
    }

    .feature-card {
      padding: 1.5rem;
    }

    .product-actions {
      flex-direction: column;
      gap: 1rem;
    }

    .btn-large {
      justify-content: center;
      padding: 1.25rem 2rem;
    }

    .btn-secondary {
      order: -1;
      align-self: flex-start;
      width: auto;
    }
  }

  @media (max-width: 480px) {
    .product-section {
      margin-bottom: 0.75rem;
    }

    .product-header {
      padding-bottom: 0.75rem;
      margin-bottom: 0.75rem;
    }

    .product-title {
      font-size: 2rem;
    }

    .price-amount {
      font-size: 1.75rem;
    }

    .product-description p {
      font-size: 1.125rem;
      line-height: 1.6;
    }

    .section-title {
      font-size: 1.25rem;
    }

    .feature-card {
      padding: 1.25rem;
    }

    .btn-large {
      padding: 1.125rem 2rem;
      font-size: 1rem;
    }

    .product-defects {
      padding: 1.5rem;
    }
  }
</style>
